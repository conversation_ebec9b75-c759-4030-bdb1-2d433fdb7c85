#!/usr/bin/env python3
"""
测试官方 langchain-ollama 包
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

async def test_official_ollama():
    """测试官方 ChatOllama"""
    print("🧪 测试官方 langchain-ollama 包...")
    
    try:
        from langchain_ollama import ChatOllama
        from langchain_core.messages import HumanMessage
        
        print("✅ 成功导入 ChatOllama")
        
        # 创建 ChatOllama 实例
        llm = ChatOllama(
            model="qwen3:1.7b",
            base_url="http://localhost:11434",
            temperature=0.7
        )
        
        print(f"✅ ChatOllama 实例创建成功")
        print(f"   模型: {llm.model}")
        print(f"   Base URL: {llm.base_url}")
        print(f"   Temperature: {llm.temperature}")
        
        # 测试简单调用
        print("\n🔄 测试简单调用...")
        message = HumanMessage(content="你好，请简单介绍一下自己")
        
        try:
            response = await llm.ainvoke([message])
            print(f"✅ 异步调用成功")
            print(f"   回复: {response.content[:100]}...")
        except Exception as e:
            print(f"❌ 异步调用失败: {e}")
            return False
        
        # 测试工具绑定
        print("\n🔧 测试工具绑定...")
        try:
            from langchain_core.tools import tool
            
            @tool
            def get_weather(location: str) -> str:
                """获取天气信息"""
                return f"{location}的天气是晴天"
            
            llm_with_tools = llm.bind_tools([get_weather])
            print(f"✅ 工具绑定成功")
            
            # 测试工具调用
            tool_message = HumanMessage(content="北京的天气怎么样？")
            tool_response = await llm_with_tools.ainvoke([tool_message])
            print(f"✅ 工具调用测试完成")
            print(f"   回复: {tool_response.content[:100]}...")
            
        except Exception as e:
            print(f"⚠️ 工具绑定测试失败: {e}")
        
        # 测试流式调用
        print("\n🔄 测试流式调用...")
        try:
            stream_content = ""
            async for chunk in llm.astream([message]):
                stream_content += chunk.content
                if len(stream_content) > 50:
                    break
            print(f"✅ 流式调用成功")
            print(f"   流式回复: {stream_content[:100]}...")
        except Exception as e:
            print(f"⚠️ 流式调用失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保已安装 langchain-ollama 包: uv add langchain-ollama")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def test_llm_loader_integration():
    """测试与 llm_loader 的集成"""
    print("\n🔗 测试与 llm_loader 的集成...")
    
    try:
        from llm_loader import load_llm_from_config
        
        # 加载 Ollama 模型
        llm = load_llm_from_config(provider="ollama")
        print(f"✅ llm_loader 集成成功")
        print(f"   模型类型: {type(llm).__name__}")
        
        # 测试调用
        from langchain_core.messages import HumanMessage
        message = HumanMessage(content="测试集成")
        
        response = await llm.ainvoke([message])
        print(f"✅ 集成调用成功")
        print(f"   回复: {response.content[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False

async def test_langgraph_integration():
    """测试与 LangGraph 的集成"""
    print("\n🔗 测试与 LangGraph 的集成...")
    
    try:
        from llm_loader import load_llm_from_config
        from langgraph.graph import StateGraph, MessagesState, START, END
        from langchain_core.messages import HumanMessage
        
        # 加载模型
        llm = load_llm_from_config(provider="ollama")
        
        # 创建简单的工作流
        async def call_model(state: MessagesState):
            messages = state["messages"]
            response = await llm.ainvoke(messages)
            return {"messages": [response]}
        
        workflow = StateGraph(MessagesState)
        workflow.add_node("agent", call_model)
        workflow.add_edge(START, "agent")
        workflow.add_edge("agent", END)
        
        app = workflow.compile()
        
        # 测试运行
        inputs = {"messages": [HumanMessage(content="测试 LangGraph 集成")]}
        result = await app.ainvoke(inputs)
        
        print(f"✅ LangGraph 集成成功")
        print(f"   回复: {result['messages'][-1].content[:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LangGraph 集成测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 官方 langchain-ollama 测试")
    print("=" * 50)
    
    # 基础测试
    success1 = await test_official_ollama()
    
    # 集成测试
    success2 = await test_llm_loader_integration()
    
    # LangGraph 集成测试
    success3 = await test_langgraph_integration()
    
    print("\n" + "=" * 50)
    print("🎯 测试结果总结:")
    print(f"   官方 ChatOllama: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"   llm_loader 集成: {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"   LangGraph 集成: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if success1 and success2 and success3:
        print("\n🎉 所有测试通过！官方 ChatOllama 可以正常工作")
        print("💡 建议：可以移除自定义 ollama_adapter.py，使用官方实现")
    else:
        print("\n⚠️ 部分测试失败，可能需要保留自定义适配器作为备选")

if __name__ == "__main__":
    asyncio.run(main())
